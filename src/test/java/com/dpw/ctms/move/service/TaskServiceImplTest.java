package com.dpw.ctms.move.service;

import com.dpw.ctms.move.constants.TaskServiceConstants;
import com.dpw.ctms.move.dto.ParamValueVehicleOperatorDTO;
import com.dpw.ctms.move.dto.taskmanager.TaskRegistrationDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.integration.adapter.TaskServiceAdapter;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationResponse;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;
import com.dpw.ctms.move.mapper.TaskInstanceRegistrationRequestMapper;
import com.dpw.ctms.move.repository.TaskRepository;
import com.dpw.ctms.move.service.impl.TaskParamService;
import com.dpw.ctms.move.service.impl.TaskServiceImpl;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

@ExtendWith(MockitoExtension.class)
class TaskServiceImplTest {

    @Mock
    private TaskInstanceRegistrationRequestMapper taskInstanceRegistrationRequestMapper;

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private TaskServiceAdapter taskServiceAdapter;

    @Mock
    private TaskParamService taskParamService;

    @Mock
    private IVehicleOperatorService vehicleOperatorService;

    @InjectMocks
    private TaskServiceImpl taskService;

    private Task testTask;
    private TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO testTaskDTO;
    private ParamValueVehicleOperatorDTO testVehicleOperator;
    private GetOperatorDetailsListResponse testOperatorDetails;
    private TransportOrder testTransportOrder;

    @BeforeEach
    void setUp() {
        testTask = new Task();
        testTask.setId(1L);
        testTask.setCode("TASK001");

        testTaskDTO = new TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO();
        testTaskDTO.setExtTaskTransactionCode("TASK001");

        testVehicleOperator = new ParamValueVehicleOperatorDTO();
        testVehicleOperator.setExternalResourceId("OP001");

        // Mock CRP details
        GetOperatorDetailsListResponse.CrpDetailsResponse crpDetails = new GetOperatorDetailsListResponse.CrpDetailsResponse();
        crpDetails.setCrpUserUUID("CRP001");

        testOperatorDetails = new GetOperatorDetailsListResponse();
        testOperatorDetails.setCrpDetails(crpDetails);

        testTransportOrder = new TransportOrder();
        testTransportOrder.setId(1L);
        testTransportOrder.setCode("TO001");
    }

    @Test
    void findTaskById_WhenTaskExists_ShouldReturnTask() {
        // Arrange
        Long taskId = 1L;
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));

        // Act
        Task result = taskService.findTaskById(taskId);

        // Assert
        assertNotNull(result);
        assertEquals(testTask.getId(), result.getId());
        verify(taskRepository).findById(taskId);
    }

    @Test
    void findTaskById_WhenTaskNotExists_ShouldThrowTMSException() {
        // Arrange
        Long taskId = 999L;
        when(taskRepository.findById(taskId)).thenReturn(Optional.empty());

        // Act & Assert
        TMSException exception = assertThrows(TMSException.class, () -> taskService.findTaskById(taskId));
        assertEquals(DATA_NOT_FOUND.name(), exception.getErrorCode());
//        assertTrue(exception.getMessage().contains(String.format(INVALID_TASK_ID, taskId)));
        verify(taskRepository).findById(taskId);
    }

    @Test
    void findTaskByCode_WhenTaskExists_ShouldReturnTask() {
        // Arrange
        String taskCode = "TASK001";
        when(taskRepository.findByCode(taskCode)).thenReturn(Optional.of(testTask));

        // Act
        Task result = taskService.findTaskByCode(taskCode);

        // Assert
        assertNotNull(result);
        assertEquals(testTask.getCode(), result.getCode());
        verify(taskRepository).findByCode(taskCode);
    }

    @Test
    void findTaskByCode_WhenTaskNotExists_ShouldThrowTMSException() {
        // Arrange
        String taskCode = "INVALID_CODE";
        when(taskRepository.findByCode(taskCode)).thenReturn(Optional.empty());

        // Act & Assert
        TMSException exception = assertThrows(TMSException.class, () -> taskService.findTaskByCode(taskCode));
        assertEquals(DATA_NOT_FOUND.name(), exception.getErrorCode());
//        assertTrue(exception.getMessage().contains(String.format(INVALID_TASK_CODE, taskCode)));
        verify(taskRepository).findByCode(taskCode);
    }

    @Test
    void saveTask_ShouldReturnSavedTask() {
        // Arrange
        when(taskRepository.save(testTask)).thenReturn(testTask);

        // Act
        Task result = taskService.saveTask(testTask);

        // Assert
        assertNotNull(result);
        assertEquals(testTask, result);
        verify(taskRepository).save(testTask);
    }

    @Test
    void instantiateTasks_WhenTaskListIsNull_ShouldReturnEarly() {
        // Act
        taskService.instantiateTasks(null, testTransportOrder);

        // Assert
        verifyNoInteractions(taskParamService);
        verifyNoInteractions(vehicleOperatorService);
        verifyNoInteractions(taskServiceAdapter);
        verifyNoInteractions(taskRepository);
    }

    @Test
    void instantiateTasks_WhenTaskListIsEmpty_ShouldReturnEarly() {
        // Act
        taskService.instantiateTasks(Collections.emptyList(), testTransportOrder);

        // Assert
        verifyNoInteractions(taskParamService);
        verifyNoInteractions(vehicleOperatorService);
        verifyNoInteractions(taskServiceAdapter);
        verifyNoInteractions(taskRepository);
    }

    @Test
    void instantiateTasks_WithValidTasks_ShouldProcessSuccessfully() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), eq(TaskServiceConstants.DEFAULT_TENANT_CODE),
                eq(TaskServiceConstants.DEFAULT_TENANT_SERVICE_CODE), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        verify(taskParamService).getVehicleOperators(testTask);
        verify(vehicleOperatorService).fetchOperatorDetails(any());
        verify(taskServiceAdapter).registerTaskInstance(any(), anyString(), anyString(), anyString());
        assertEquals("REG001", testTask.getExternalTaskRegistrationCode());
    }

    @Test
    void instantiateTasks_WithTasksHavingNoVehicleOperators_ShouldProcessWithControllerRoleOnly() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> emptyVehicleOperators = Collections.emptyList();

        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(emptyVehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(Collections.emptyMap());
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        verify(taskParamService).getVehicleOperators(testTask);
        verify(vehicleOperatorService).fetchOperatorDetails(any());
        verify(taskServiceAdapter).registerTaskInstance(any(), anyString(), anyString(), anyString());
    }

    @Test
    void instantiateTasks_WithVehicleOperatorsButNullExternalResourceId_ShouldSkipOperator() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        ParamValueVehicleOperatorDTO operatorWithNullId = new ParamValueVehicleOperatorDTO();
        operatorWithNullId.setExternalResourceId(null);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(operatorWithNullId);

        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(Collections.emptyMap());
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        verify(vehicleOperatorService).fetchOperatorDetails(Collections.emptyMap());
    }

    @Test
    void instantiateTasks_WithOperatorDetailsButNoCrpDetails_ShouldLogWarning() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);

        GetOperatorDetailsListResponse operatorWithoutCrp = new GetOperatorDetailsListResponse();
        operatorWithoutCrp.setCrpDetails(null);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", operatorWithoutCrp);

        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        // No database save should occur in instantiateTasks
    }

    @Test
    void instantiateTasks_WithMissingOperatorDetails_ShouldLogWarning() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);
        Map<String, GetOperatorDetailsListResponse> emptyOperatorDetailsMap = Collections.emptyMap();

        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(emptyOperatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        // No database save should occur in instantiateTasks
    }

    @Test
    void instantiateTasks_WithResponseForNonExistentTask_ShouldLogWarning() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> emptyVehicleOperators = Collections.emptyList();

        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("NON_EXISTENT_TASK");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(emptyVehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(Collections.emptyMap());
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        assertNull(testTask.getExternalTaskRegistrationCode());
    }

    @Test
    void instantiateTasks_WithMultipleTasks_ShouldProcessAllTasks() {
        // Arrange
        Task task2 = new Task();
        task2.setId(2L);
        task2.setCode("TASK002");

        List<Task> taskList = Arrays.asList(testTask, task2);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO task2DTO =
                new TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO();
        task2DTO.setExtTaskTransactionCode("TASK002");

        TaskInstanceRegistrationResponse response1 = new TaskInstanceRegistrationResponse();
        response1.setExtTaskTransactionCode("TASK001");
        response1.setTaskRegistrationCode("REG001");

        TaskInstanceRegistrationResponse response2 = new TaskInstanceRegistrationResponse();
        response2.setExtTaskTransactionCode("TASK002");
        response2.setTaskRegistrationCode("REG002");

        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response1, response2);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(taskParamService.getVehicleOperators(task2)).thenReturn(Collections.emptyList());
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(task2))
                .thenReturn(task2DTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        verify(taskParamService).getVehicleOperators(testTask);
        verify(taskParamService).getVehicleOperators(task2);
        assertEquals("REG001", testTask.getExternalTaskRegistrationCode());
        assertEquals("REG002", task2.getExternalTaskRegistrationCode());
    }

    // ========== Tests for populateCrpIdInVehicleOperatorResources method ==========

    @Test
    void populateCrpIdInVehicleOperatorResources_WithValidData_ShouldPopulateCrpId() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);

        // Create VehicleOperatorResource with external resource ID
        VehicleOperatorResource vehicleOperatorResource = new VehicleOperatorResource();
        vehicleOperatorResource.setExternalResourceId("OP001");
        vehicleOperatorResource.setCrpId(null); // Initially null

        // Create Trip with VehicleOperatorResource
        Trip trip = new Trip();
        trip.setVehicleOperatorResources(Set.of(vehicleOperatorResource));

        // Set up TransportOrder with trips
        testTransportOrder.setTrips(Set.of(trip));

        // Create operator details map with matching external resource ID
        testOperatorDetails.setId(1L); // This will be converted to string "1" for comparison
        vehicleOperatorResource.setExternalResourceId("1"); // Match the ID
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(Arrays.asList(new TaskInstanceRegistrationResponse()));

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        assertEquals("CRP001", vehicleOperatorResource.getCrpId());
        verify(taskParamService).getVehicleOperators(testTask);
        verify(vehicleOperatorService).fetchOperatorDetails(any());
    }

    @Test
    void populateCrpIdInVehicleOperatorResources_WithNullTrips_ShouldLogWarningAndContinue() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        testTransportOrder.setTrips(null);

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(Arrays.asList(new TaskInstanceRegistrationResponse()));

        // Act & Assert - Should not throw exception
        assertDoesNotThrow(() -> taskService.instantiateTasks(taskList, testTransportOrder));

        verify(taskParamService).getVehicleOperators(testTask);
        verify(vehicleOperatorService).fetchOperatorDetails(any());
    }

    @Test
    void populateCrpIdInVehicleOperatorResources_WithNullVehicleOperatorResources_ShouldContinue() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);

        Trip trip = new Trip();
        trip.setVehicleOperatorResources(null);
        testTransportOrder.setTrips(Set.of(trip));

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(Arrays.asList(new TaskInstanceRegistrationResponse()));

        // Act & Assert - Should not throw exception
        assertDoesNotThrow(() -> taskService.instantiateTasks(taskList, testTransportOrder));

        verify(taskParamService).getVehicleOperators(testTask);
        verify(vehicleOperatorService).fetchOperatorDetails(any());
    }

    @Test
    void populateCrpIdInVehicleOperatorResources_WithMultipleTripsAndResources_ShouldPopulateAll() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);

        // Create multiple VehicleOperatorResources
        VehicleOperatorResource resource1 = new VehicleOperatorResource();
        resource1.setExternalResourceId("1");
        resource1.setCrpId(null);

        VehicleOperatorResource resource2 = new VehicleOperatorResource();
        resource2.setExternalResourceId("2");
        resource2.setCrpId(null);

        // Create multiple trips
        Trip trip1 = new Trip();
        trip1.setVehicleOperatorResources(Set.of(resource1));

        Trip trip2 = new Trip();
        trip2.setVehicleOperatorResources(Set.of(resource2));

        testTransportOrder.setTrips(Set.of(trip1, trip2));

        // Create operator details for both resources
        GetOperatorDetailsListResponse operatorDetails1 = new GetOperatorDetailsListResponse();
        operatorDetails1.setId(1L);
        GetOperatorDetailsListResponse.CrpDetailsResponse crpDetails1 = new GetOperatorDetailsListResponse.CrpDetailsResponse();
        crpDetails1.setCrpUserUUID("CRP001");
        operatorDetails1.setCrpDetails(crpDetails1);

        GetOperatorDetailsListResponse operatorDetails2 = new GetOperatorDetailsListResponse();
        operatorDetails2.setId(2L);
        GetOperatorDetailsListResponse.CrpDetailsResponse crpDetails2 = new GetOperatorDetailsListResponse.CrpDetailsResponse();
        crpDetails2.setCrpUserUUID("CRP002");
        operatorDetails2.setCrpDetails(crpDetails2);

        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", operatorDetails1, "OP002", operatorDetails2);

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(Arrays.asList(new TaskInstanceRegistrationResponse()));

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        assertEquals("CRP001", resource1.getCrpId());
        assertEquals("CRP002", resource2.getCrpId());
    }

    // ========== Tests for extractCrpIdFromOperatorDetails method ==========

    @Test
    void extractCrpIdFromOperatorDetails_WithValidData_ShouldReturnCrpId() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);

        VehicleOperatorResource vehicleOperatorResource = new VehicleOperatorResource();
        vehicleOperatorResource.setExternalResourceId("1");

        Trip trip = new Trip();
        trip.setVehicleOperatorResources(Set.of(vehicleOperatorResource));
        testTransportOrder.setTrips(Set.of(trip));

        // Set up operator details with ID that matches external resource ID
        testOperatorDetails.setId(1L);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(Arrays.asList(new TaskInstanceRegistrationResponse()));

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        assertEquals("CRP001", vehicleOperatorResource.getCrpId());
    }

    @Test
    void extractCrpIdFromOperatorDetails_WithNullExternalResourceId_ShouldSetNullCrpId() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);

        VehicleOperatorResource vehicleOperatorResource = new VehicleOperatorResource();
        vehicleOperatorResource.setExternalResourceId(null); // Null external resource ID

        Trip trip = new Trip();
        trip.setVehicleOperatorResources(Set.of(vehicleOperatorResource));
        testTransportOrder.setTrips(Set.of(trip));

        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(Arrays.asList(new TaskInstanceRegistrationResponse()));

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        assertNull(vehicleOperatorResource.getCrpId());
    }

    @Test
    void extractCrpIdFromOperatorDetails_WithNonMatchingExternalResourceId_ShouldSetNullCrpId() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);

        VehicleOperatorResource vehicleOperatorResource = new VehicleOperatorResource();
        vehicleOperatorResource.setExternalResourceId("999"); // Non-matching ID

        Trip trip = new Trip();
        trip.setVehicleOperatorResources(Set.of(vehicleOperatorResource));
        testTransportOrder.setTrips(Set.of(trip));

        testOperatorDetails.setId(1L); // Different ID
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(Arrays.asList(new TaskInstanceRegistrationResponse()));

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        assertNull(vehicleOperatorResource.getCrpId());
    }

    @Test
    void extractCrpIdFromOperatorDetails_WithNullCrpDetails_ShouldSetNullCrpId() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);

        VehicleOperatorResource vehicleOperatorResource = new VehicleOperatorResource();
        vehicleOperatorResource.setExternalResourceId("1");

        Trip trip = new Trip();
        trip.setVehicleOperatorResources(Set.of(vehicleOperatorResource));
        testTransportOrder.setTrips(Set.of(trip));

        // Create operator details without CRP details
        GetOperatorDetailsListResponse operatorDetailsWithoutCrp = new GetOperatorDetailsListResponse();
        operatorDetailsWithoutCrp.setId(1L);
        operatorDetailsWithoutCrp.setCrpDetails(null); // Null CRP details

        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", operatorDetailsWithoutCrp);

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(Arrays.asList(new TaskInstanceRegistrationResponse()));

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        assertNull(vehicleOperatorResource.getCrpId());
    }

    @Test
    void extractCrpIdFromOperatorDetails_WithNullOperatorId_ShouldSetNullCrpId() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);

        VehicleOperatorResource vehicleOperatorResource = new VehicleOperatorResource();
        vehicleOperatorResource.setExternalResourceId("1");

        Trip trip = new Trip();
        trip.setVehicleOperatorResources(Set.of(vehicleOperatorResource));
        testTransportOrder.setTrips(Set.of(trip));

        // Create operator details with null ID
        GetOperatorDetailsListResponse operatorDetailsWithNullId = new GetOperatorDetailsListResponse();
        operatorDetailsWithNullId.setId(null); // Null ID
        operatorDetailsWithNullId.setCrpDetails(testOperatorDetails.getCrpDetails());

        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", operatorDetailsWithNullId);

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(Arrays.asList(new TaskInstanceRegistrationResponse()));

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        assertNull(vehicleOperatorResource.getCrpId());
    }

    @Test
    void extractCrpIdFromOperatorDetails_WithEmptyOperatorDetailsMap_ShouldSetNullCrpId() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);

        VehicleOperatorResource vehicleOperatorResource = new VehicleOperatorResource();
        vehicleOperatorResource.setExternalResourceId("1");

        Trip trip = new Trip();
        trip.setVehicleOperatorResources(Set.of(vehicleOperatorResource));
        testTransportOrder.setTrips(Set.of(trip));

        Map<String, GetOperatorDetailsListResponse> emptyOperatorDetailsMap = Collections.emptyMap();

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(emptyOperatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(Arrays.asList(new TaskInstanceRegistrationResponse()));

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        assertNull(vehicleOperatorResource.getCrpId());
    }

    // ========== Integration test for complete flow ==========

    @Test
    void instantiateTasks_WithCrpIdPopulation_ShouldCompleteFullFlow() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);

        // Create VehicleOperatorResource
        VehicleOperatorResource vehicleOperatorResource = new VehicleOperatorResource();
        vehicleOperatorResource.setExternalResourceId("1");
        vehicleOperatorResource.setCrpId(null);

        Trip trip = new Trip();
        trip.setVehicleOperatorResources(Set.of(vehicleOperatorResource));
        testTransportOrder.setTrips(Set.of(trip));

        testOperatorDetails.setId(1L);
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap =
                Map.of("OP001", testOperatorDetails);

        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(
                TaskInstanceRegistrationResponse.builder()
                        .extTaskTransactionCode("TASK001")
                        .taskRegistrationCode("REG001")
                        .build()
        );

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(vehicleOperatorService.fetchOperatorDetails(any())).thenReturn(operatorDetailsMap);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.instantiateTasks(taskList, testTransportOrder);

        // Assert
        // Verify CRP ID was populated
        assertEquals("CRP001", vehicleOperatorResource.getCrpId());

        // Verify task registration code was set
        assertEquals("REG001", testTask.getExternalTaskRegistrationCode());

        // Verify all services were called
        verify(taskParamService).getVehicleOperators(testTask);
        verify(vehicleOperatorService).fetchOperatorDetails(any());
        verify(taskServiceAdapter).registerTaskInstance(any(), anyString(), anyString(), anyString());

        // Verify no direct task repository save was called
        verify(taskRepository, never()).saveAll(any());
        verify(taskRepository, never()).save(any());
    }
}